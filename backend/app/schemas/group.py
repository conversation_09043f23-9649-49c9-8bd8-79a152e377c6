from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from .user import User


class GroupBase(BaseModel):
    name: str
    description: Optional[str] = None


class GroupCreate(GroupBase):
    pass


class GroupUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None


class GroupInDB(GroupBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    owner_id: int

    class Config:
        from_attributes = True


class Group(GroupInDB):
    members: List[User] = []
