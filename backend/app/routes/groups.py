from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from ..database import get_db
from ..schemas.group import Group, GroupCreate, GroupUpdate
from ..services.group import (
    get_group, get_user_groups, create_group, update_group,
    add_member_to_group, remove_member_from_group, delete_group
)
from ..services.auth import get_current_active_user
from ..models.user import User as UserModel

router = APIRouter(prefix="/groups", tags=["groups"])


@router.get("/", response_model=List[Group])
def read_user_groups(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """Get current user's groups"""
    groups = get_user_groups(db, user_id=current_user.id)
    return groups


@router.post("/", response_model=Group)
def create_group_for_user(
    group: GroupCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """Create a new group"""
    return create_group(db=db, group=group, user_id=current_user.id)


@router.get("/{group_id}", response_model=Group)
def read_group(
    group_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """Get group by ID"""
    db_group = get_group(db, group_id=group_id)
    if db_group is None:
        raise HTTPException(status_code=404, detail="Group not found")
    
    # Check if user is a member of the group
    if current_user not in db_group.members:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return db_group


@router.put("/{group_id}", response_model=Group)
def update_group_by_id(
    group_id: int,
    group_update: GroupUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """Update group"""
    return update_group(db=db, group_id=group_id, group_update=group_update, user_id=current_user.id)


@router.post("/{group_id}/members/{user_id}", response_model=Group)
def add_member(
    group_id: int,
    user_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """Add member to group"""
    return add_member_to_group(db=db, group_id=group_id, user_id=current_user.id, member_id=user_id)


@router.delete("/{group_id}/members/{user_id}", response_model=Group)
def remove_member(
    group_id: int,
    user_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """Remove member from group"""
    return remove_member_from_group(db=db, group_id=group_id, user_id=current_user.id, member_id=user_id)


@router.delete("/{group_id}")
def delete_group_by_id(
    group_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """Delete group"""
    delete_group(db=db, group_id=group_id, user_id=current_user.id)
    return {"message": "Group deleted successfully"}
