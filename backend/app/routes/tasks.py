from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from ..database import get_db
from ..schemas.task import Task, TaskCreate, TaskUpdate
from ..services.task import (
    get_task, get_user_tasks, create_task, update_task, 
    complete_task, delete_task
)
from ..services.auth import get_current_active_user
from ..models.user import User as UserModel

router = APIRouter(prefix="/tasks", tags=["tasks"])


@router.get("/", response_model=List[Task])
def read_tasks(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """Get current user's tasks"""
    tasks = get_user_tasks(db, user_id=current_user.id, skip=skip, limit=limit)
    return tasks


@router.post("/", response_model=Task)
def create_task_for_user(
    task: TaskCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """Create a new task for current user"""
    return create_task(db=db, task=task, user_id=current_user.id)


@router.get("/{task_id}", response_model=Task)
def read_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """Get task by ID"""
    db_task = get_task(db, task_id=task_id)
    if db_task is None:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # Check if user owns the task
    if db_task.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return db_task


@router.put("/{task_id}", response_model=Task)
def update_task_by_id(
    task_id: int,
    task_update: TaskUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """Update task"""
    return update_task(db=db, task_id=task_id, task_update=task_update, user_id=current_user.id)


@router.post("/{task_id}/complete", response_model=Task)
def complete_task_by_id(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """Mark task as completed"""
    return complete_task(db=db, task_id=task_id, user_id=current_user.id)


@router.delete("/{task_id}")
def delete_task_by_id(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """Delete task"""
    delete_task(db=db, task_id=task_id, user_id=current_user.id)
    return {"message": "Task deleted successfully"}
