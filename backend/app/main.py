import warnings
# Suppress bcrypt version warning
warnings.filterwarnings("ignore", message=".*bcrypt.*", category=UserWarning)

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .config import settings
from .routes import auth, users, tasks, groups

app = FastAPI(
    title=settings.app_name,
    description="A FastAPI backend for a habit tracking game",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router)
app.include_router(users.router)
app.include_router(tasks.router)
app.include_router(groups.router)


@app.get("/")
def read_root():
    """Root endpoint"""
    return {"message": "Welcome to the Habit Tracker Game API"}


@app.get("/health")
def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}
