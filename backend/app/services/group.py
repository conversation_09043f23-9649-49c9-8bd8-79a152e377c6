from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from ..models.group import Group
from ..models.user import User
from ..schemas.group import GroupCreate, GroupUpdate


def get_group(db: Session, group_id: int):
    """Get group by ID"""
    return db.query(Group).filter(Group.id == group_id).first()


def get_groups(db: Session, skip: int = 0, limit: int = 100):
    """Get list of groups"""
    return db.query(Group).offset(skip).limit(limit).all()


def get_user_groups(db: Session, user_id: int):
    """Get groups for a specific user"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        return []
    return user.groups


def create_group(db: Session, group: GroupCreate, user_id: int):
    """Create a new group"""
    db_group = Group(
        name=group.name,
        description=group.description,
        owner_id=user_id
    )
    db.add(db_group)
    db.commit()
    db.refresh(db_group)
    
    # Add creator as a member
    user = db.query(User).filter(User.id == user_id).first()
    if user:
        db_group.members.append(user)
        db.commit()
    
    return db_group


def update_group(db: Session, group_id: int, group_update: GroupUpdate, user_id: int):
    """Update group"""
    db_group = get_group(db, group_id)
    if not db_group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    # Check if user owns the group
    if db_group.owner_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    update_data = group_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_group, field, value)
    
    db.commit()
    db.refresh(db_group)
    return db_group


def add_member_to_group(db: Session, group_id: int, user_id: int, member_id: int):
    """Add member to group"""
    db_group = get_group(db, group_id)
    if not db_group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    # Check if user owns the group
    if db_group.owner_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    member = db.query(User).filter(User.id == member_id).first()
    if not member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    if member not in db_group.members:
        db_group.members.append(member)
        db.commit()
    
    return db_group


def remove_member_from_group(db: Session, group_id: int, user_id: int, member_id: int):
    """Remove member from group"""
    db_group = get_group(db, group_id)
    if not db_group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    # Check if user owns the group
    if db_group.owner_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    member = db.query(User).filter(User.id == member_id).first()
    if member and member in db_group.members:
        db_group.members.remove(member)
        db.commit()
    
    return db_group


def delete_group(db: Session, group_id: int, user_id: int):
    """Delete group"""
    db_group = get_group(db, group_id)
    if not db_group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    # Check if user owns the group
    if db_group.owner_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    db.delete(db_group)
    db.commit()
    return db_group
