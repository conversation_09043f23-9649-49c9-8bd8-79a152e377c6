"""Add status field to Task model

Revision ID: add_task_status
Revises: 0b3247b91912
Create Date: 2025-01-11 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_task_status'
down_revision = '0b3247b91912'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create the enum type
    task_status_enum = sa.Enum('TODO', 'IN_PROGRESS', 'DONE', name='taskstatus')
    task_status_enum.create(op.get_bind())
    
    # Add the status column with default value
    op.add_column('tasks', sa.Column('status', task_status_enum, nullable=False, server_default='TODO'))


def downgrade() -> None:
    # Remove the status column
    op.drop_column('tasks', 'status')
    
    # Drop the enum type
    task_status_enum = sa.Enum('TODO', 'IN_PROGRESS', 'DONE', name='taskstatus')
    task_status_enum.drop(op.get_bind())
