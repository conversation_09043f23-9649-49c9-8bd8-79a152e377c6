version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_USER: ${DB_USER:-user}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password}
      POSTGRES_DB: ${DB_NAME:-habit_tracker}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-user} -d ${DB_NAME:-habit_tracker}"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: postgresql://${DB_USER:-user}:${DB_PASSWORD:-password}@db:5432/${DB_NAME:-habit_tracker}
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-change-this-in-production}
      DB_HOST: db
      DB_PORT: 5432
      DB_USER: ${DB_USER:-user}
      DB_PASSWORD: ${DB_PASSWORD:-password}
      DB_NAME: ${DB_NAME:-habit_tracker}
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./backend:/app
    restart: unless-stopped

volumes:
  postgres_data:
