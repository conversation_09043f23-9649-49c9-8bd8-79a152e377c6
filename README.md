# Habit Tracker Game

A full-stack habit tracking game with character progression, featuring a FastAPI backend and React + PixiJS frontend.

## Features

### Backend (FastAPI)
- **JWT-based Authentication**: Secure user registration and login
- **Task Management**: Create, update, complete, and delete tasks
- **Group Management**: Create groups and manage members
- **PostgreSQL Database**: Persistent data storage with Alembic migrations
- **Docker Support**: Easy deployment with Docker Compose
- **Clean Architecture**: Separated concerns with models, schemas, services, and routes

### Frontend (React + PixiJS)
- **Interactive Task Dashboard**: Task management with completion checkboxes
- **PixiJS Character System**: Layered sprite rendering with equipment and animations
- **Character Progression**: Level up system with experience points and stat growth
- **Real-time Animations**: Character animations triggered on task completion
- **Modern UI**: Responsive design built with React and Tailwind CSS
- **State Management**: Zustand for efficient global state management

## Project Structure

```
├── backend/             # FastAPI backend
│   ├── app/
│   │   ├── models/      # SQLAlchemy models
│   │   ├── schemas/     # Pydantic schemas
│   │   ├── routes/      # FastAPI route handlers
│   │   ├── services/    # Business logic
│   │   ├── utils/       # Utility functions
│   │   ├── config.py    # Configuration settings
│   │   ├── database.py  # Database connection
│   │   └── main.py      # FastAPI application
│   ├── alembic/         # Database migrations
│   ├── requirements.txt # Python dependencies
│   └── Dockerfile       # Docker configuration
├── client/              # React + PixiJS frontend
│   ├── src/
│   │   ├── components/  # React components
│   │   ├── store/       # Zustand state management
│   │   ├── api/         # API service layer
│   │   ├── hooks/       # Custom React hooks
│   │   ├── types/       # TypeScript definitions
│   │   └── utils/       # Utility functions
│   ├── package.json     # Node.js dependencies
│   └── vite.config.ts   # Vite configuration
├── docker-compose.yaml  # Multi-service setup
└── .env                 # Environment variables
```

## Quick Start

### Prerequisites

- Docker and Docker Compose
- Node.js 18+ and npm (for frontend development)
- Git

### Running the Full Stack

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd kanban-rpg
   ```

2. **Start the backend with Docker**:
   ```bash
   docker-compose up -d
   ```

3. **Install and start the frontend**:
   ```bash
   cd client
   npm install
   npm run dev
   ```

4. **Access the application**:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API docs: http://localhost:8000/docs

### Environment Variables

The application uses environment variables defined in `.env`:

- `DB_USER`: Database username (default: user)
- `DB_PASSWORD`: Database password (default: password)
- `DB_NAME`: Database name (default: habit_tracker)
- `SECRET_KEY`: JWT secret key (change in production!)
- `ACCESS_TOKEN_EXPIRE_MINUTES`: Token expiration time (default: 30)

## Frontend Features

### Character System
- **Layered Sprites**: Base character with equipment overlays (weapon, armor)
- **Animations**: Celebrate on task completion, level-up effects, attack animations
- **Progression**: Gain 25 XP per completed task, level up every 100 XP
- **Stats**: Strength, Defense, and Speed increase with each level

### Task Management
- **Interactive Dashboard**: Create, edit, complete, and delete tasks
- **Due Dates**: Optional due dates with overdue indicators
- **Filtering**: View all, pending, completed, or overdue tasks
- **Real-time Updates**: Immediate UI updates with optimistic updates

### User Experience
- **Responsive Design**: Works on desktop and mobile devices
- **Modern UI**: Clean interface built with Tailwind CSS
- **Smooth Animations**: PixiJS-powered character animations
- **State Persistence**: Login state and character progress saved locally

## API Endpoints

### Authentication
- `POST /auth/register` - Register a new user
- `POST /auth/login` - Login with form data
- `POST /auth/login-json` - Login with JSON payload

### Users
- `GET /users/me` - Get current user profile
- `PUT /users/me` - Update current user
- `DELETE /users/me` - Delete current user
- `GET /users/` - List all users
- `GET /users/{user_id}` - Get user by ID

### Tasks
- `GET /tasks/` - Get current user's tasks
- `POST /tasks/` - Create a new task
- `GET /tasks/{task_id}` - Get task by ID
- `PUT /tasks/{task_id}` - Update task
- `POST /tasks/{task_id}/complete` - Mark task as completed
- `DELETE /tasks/{task_id}` - Delete task

### Groups
- `GET /groups/` - Get current user's groups
- `POST /groups/` - Create a new group
- `GET /groups/{group_id}` - Get group by ID
- `PUT /groups/{group_id}` - Update group
- `POST /groups/{group_id}/members/{user_id}` - Add member to group
- `DELETE /groups/{group_id}/members/{user_id}` - Remove member from group
- `DELETE /groups/{group_id}` - Delete group

## Development

### Local Development Setup

1. **Install Python dependencies**:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **Set up PostgreSQL database** (or use Docker for just the database):
   ```bash
   docker run --name postgres-dev -e POSTGRES_USER=user -e POSTGRES_PASSWORD=password -e POSTGRES_DB=habit_tracker -p 5432:5432 -d postgres:15
   ```

3. **Run migrations**:
   ```bash
   cd backend
   alembic upgrade head
   ```

4. **Start the development server**:
   ```bash
   cd backend
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

### Database Migrations

Create a new migration:
```bash
cd backend
alembic revision --autogenerate -m "Description of changes"
```

Apply migrations:
```bash
cd backend
alembic upgrade head
```

### Testing the API

You can test the API using the interactive documentation at http://localhost:8000/docs or with curl:

```bash
# Register a new user
curl -X POST "http://localhost:8000/auth/register" \
     -H "Content-Type: application/json" \
     -d '{"username": "testuser", "email": "<EMAIL>", "password": "testpass123"}'

# Login
curl -X POST "http://localhost:8000/auth/login-json" \
     -H "Content-Type: application/json" \
     -d '{"username": "testuser", "password": "testpass123"}'

# Create a task (replace TOKEN with the access token from login)
curl -X POST "http://localhost:8000/tasks/" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer TOKEN" \
     -d '{"title": "My first task", "description": "This is a test task"}'

# Complete a task (replace 1 with actual task ID)
curl -X POST "http://localhost:8000/tasks/1/complete" \
     -H "Authorization: Bearer TOKEN"
```

## Production Deployment

For production deployment:

1. **Update environment variables** in `.env`:
   - Change `SECRET_KEY` to a secure random string
   - Use strong database credentials
   - Set `DEBUG=false`

2. **Use a production WSGI server** (already configured in Docker):
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 8000
   ```

3. **Set up reverse proxy** (nginx, traefik, etc.) for HTTPS and load balancing

4. **Configure CORS** in `app/main.py` to allow only your frontend domains

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.