# 🎨 Design Tokens

This folder contains the source of truth for all design tokens in the project.

## 📝 Quick Guide

### Adding New Colors

1. **Edit `colors.json`:**
   ```json
   {
     "color": {
       "primary": {
         "500": { "value": "#2196f3", "type": "color" }
       }
     }
   }
   ```

2. **Build tokens:**
   ```bash
   yarn build-tokens
   ```

3. **Use in CSS:**
   ```css
   .my-component {
     color: var(--color-primary-500);
   }
   ```

4. **Use in TypeScript:**
   ```typescript
   import { Colors } from '@/styles/colors';
   const color = Colors.Primary500;
   ```

## 🌙 Dark Mode

Add dark variants in the `color-dark` section:

```json
{
  "color": {
    "background": { "value": "#ffffff", "type": "color" }
  },
  "color-dark": {
    "background": { "value": "#1a1a1a", "type": "color" }
  }
}
```

## 🚀 Auto-Rebuild

For automatic rebuilding during development:

```bash
yarn dev:with-tokens
```

Now just save `colors.json` and tokens rebuild automatically!

## 📋 Naming

- Use semantic names: `primary`, `success`, `warning`, `error`
- Use numeric scales: `100`, `200`, `500`, `900`
- Avoid color names: Use `primary` not `blue`

## 📁 Generated Files

- `../build/css/variables.css` - CSS custom properties
- `../src/styles/colors.ts` - TypeScript Colors object

---

💡 **Need more details?** See the main `TOKENS.md` file in the project root.
