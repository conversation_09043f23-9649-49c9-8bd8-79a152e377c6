import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

/**
 * File watcher for tokens directory
 * Automatically rebuilds tokens when files change
 */

const TOKENS_DIR = 'tokens';
const DEBOUNCE_DELAY = 500; // ms

let buildTimeout;
let isBuilding = false;

function buildTokens() {
  if (isBuilding) {
    console.log('⏳ Build already in progress, skipping...');
    return;
  }

  isBuilding = true;
  console.log('\n🔄 Token files changed, rebuilding...');
  
  try {
    execSync('node build-tokens.js', { stdio: 'inherit' });
    console.log('✅ Tokens rebuilt successfully!\n');
  } catch (error) {
    console.error('❌ Token build failed:', error.message);
  } finally {
    isBuilding = false;
  }
}

function debouncedBuild() {
  clearTimeout(buildTimeout);
  buildTimeout = setTimeout(buildTokens, DEBOUNCE_DELAY);
}

function watchTokens() {
  console.log('👀 Watching tokens directory for changes...');
  console.log(`📁 Watching: ${path.resolve(TOKENS_DIR)}`);
  console.log('🔄 Changes will automatically trigger token rebuilds');
  console.log('⏹️  Press Ctrl+C to stop watching\n');

  // Initial build
  buildTokens();

  // Watch for changes (without recursive option for compatibility)
  try {
    fs.watch(TOKENS_DIR, (eventType, filename) => {
      if (filename && filename.endsWith('.json')) {
        console.log(`📝 Detected change: ${filename}`);
        debouncedBuild();
      }
    });
  } catch (error) {
    console.error('❌ Failed to watch directory:', error.message);
    console.log('💡 You can manually run "yarn build-tokens" when you make changes');
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Stopping token watcher...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 Stopping token watcher...');
  process.exit(0);
});

// Start watching
watchTokens();
