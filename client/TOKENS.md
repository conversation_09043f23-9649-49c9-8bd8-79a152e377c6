# 🎨 Design Tokens with Style Dictionary

This project uses Style Dictionary to manage design tokens and automatically generate CSS variables and TypeScript color objects.

## 📁 File Structure

```
client/
├── tokens/
│   └── colors.json          # Source of truth for all colors
├── build/css/
│   └── variables.css        # Generated CSS variables
├── src/styles/
│   └── colors.ts           # Generated TypeScript Colors object
├── build-tokens.js         # Custom build script
├── watch-tokens.js         # File watcher for auto-rebuilding
└── style-dictionary.config.json  # Style Dictionary configuration
```

## 🚀 Quick Start

### Development with Auto-Rebuild
```bash
# Start development server with automatic token rebuilding
yarn dev:with-tokens
```

This runs both:
- 👀 **Token Watcher** - Automatically rebuilds when `tokens/colors.json` changes
- ⚡ **Vite Dev Server** - Your React app with hot reload

### Manual Token Building
```bash
# Build tokens once
yarn build-tokens

# Watch tokens for changes (without dev server)
yarn watch-tokens
```

## 🎨 Adding/Modifying Colors

### 1. Edit the tokens file
```json
// tokens/colors.json
{
  "color": {
    "primary": {
      "500": { "value": "#2196f3", "type": "color" }
    },
    "secondary": {
      "500": { "value": "#9c27b0", "type": "color" }
    }
  },
  "color-dark": {
    "primary": {
      "500": { "value": "#42a5f5", "type": "color" }
    }
  }
}
```

### 2. Automatic Rebuild
If you're using `yarn dev:with-tokens`, tokens will rebuild automatically!

Otherwise, run:
```bash
yarn build-tokens
```

### 3. Use the colors

**In CSS:**
```css
.my-component {
  background-color: var(--color-primary-500);
  color: var(--color-secondary-500);
}

/* Dark mode automatically handled */
[data-theme="dark"] .my-component {
  /* --color-primary-500 will use dark variant */
}
```

**In TypeScript:**
```typescript
import { Colors } from '@/styles/colors';

const primaryColor = Colors.Primary500;
const secondaryColor = Colors.Secondary500;
```

## 🌙 Dark Mode Support

Add dark variants in the `color-dark` section:

```json
{
  "color": {
    "background": {
      "primary": { "value": "#ffffff", "type": "color" }
    }
  },
  "color-dark": {
    "background": {
      "primary": { "value": "#1a1a1a", "type": "color" }
    }
  }
}
```

Generated CSS:
```css
:root {
  --color-background-primary: #ffffff;
}

[data-theme="dark"] {
  --color-background-primary: #1a1a1a;
}
```

## 📝 Naming Conventions

### Tokens (JSON)
- Use kebab-case: `primary-500`, `background-secondary`
- Include semantic names: `primary`, `secondary`, `success`, `warning`, `error`
- Use numeric scales: `100`, `200`, `300`, `400`, `500`, `600`, `700`, `800`, `900`

### Generated CSS Variables
- Format: `--color-{name}-{scale}`
- Examples: `--color-primary-500`, `--color-gray-100`

### Generated TypeScript
- Format: `{Name}{Scale}`
- Examples: `Colors.Primary500`, `Colors.Gray100`

## 🔧 Available Scripts

| Script | Description |
|--------|-------------|
| `yarn dev:with-tokens` | Start dev server with automatic token rebuilding |
| `yarn dev` | Start dev server only |
| `yarn build-tokens` | Build tokens once |
| `yarn watch-tokens` | Watch tokens for changes |
| `yarn build` | Build production app (includes token build) |

## 💡 Best Practices

1. **Always use semantic names** instead of color names
   - ✅ `primary-500`, `success-500`, `warning-500`
   - ❌ `blue-500`, `green-500`, `yellow-500`

2. **Commit generated files** to version control
   - Include `build/css/variables.css` and `src/styles/colors.ts` in git

3. **Use the automated workflow**
   - Run `yarn dev:with-tokens` during development
   - Tokens rebuild automatically when you save changes

4. **Test dark mode**
   - Add `data-theme="dark"` to your HTML element
   - Verify dark variants look correct

5. **Document color usage**
   - Add comments in `tokens/colors.json` to explain color purposes

## 🐛 Troubleshooting

### Tokens not updating?
1. Check if the watcher is running (`yarn watch-tokens`)
2. Manually run `yarn build-tokens`
3. Restart the dev server

### CSS variables not available?
1. Ensure `build/css/variables.css` is imported in `src/index.css`
2. Check browser dev tools for CSS variable values

### TypeScript errors?
1. Restart TypeScript server in your IDE
2. Check that `src/styles/colors.ts` was generated correctly

## 📚 Learn More

- [Style Dictionary Documentation](https://amzn.github.io/style-dictionary/)
- [CSS Custom Properties (MDN)](https://developer.mozilla.org/en-US/docs/Web/CSS/--*)
- [Design Tokens Community Group](https://design-tokens.github.io/community-group/)
