import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// Simple approach - use the working config and post-process files

async function buildTokens() {
  try {
    // Run the basic build (compatible with yarn v1)
    execSync('npx style-dictionary build --config style-dictionary.config.json', { stdio: 'inherit' });

    // Post-process the TypeScript file to match our requirements
    const tsFilePath = 'src/styles/colors.ts';
    const tsContent = fs.readFileSync(tsFilePath, 'utf8');

    // Extract color values and create the Colors object (exclude dark colors)
    const colorMatches = tsContent.match(/export const Color(\w+) = "([^"]+)";/g);

    if (colorMatches) {
      const colorsObject = colorMatches
        .filter(match => !match.includes('Dark')) // Exclude dark colors
        .map(match => {
          const [, name, value] = match.match(/export const Color(\w+) = "([^"]+)";/);
          return `  ${name}: '${value}',`;
        }).join('\n');

      const newTsContent = `/**
 * Do not edit directly
 * Generated on ${new Date().toUTCString()}
 */

export const Colors = {
${colorsObject}
};

export default Colors;
`;

      fs.writeFileSync(tsFilePath, newTsContent);
    }

    // Post-process CSS file to separate light and dark mode variables
    const cssFilePath = 'build/css/variables.css';
    let cssContent = fs.readFileSync(cssFilePath, 'utf8');

    // Extract variables from the CSS content
    const variableRegex = /--color-([^:]+):\s*([^;]+);/g;
    const lightVars = [];
    const darkVars = [];

    let match;
    while ((match = variableRegex.exec(cssContent)) !== null) {
      const [fullMatch, varName, value] = match;
      if (varName.startsWith('dark-')) {
        // Convert dark variables to override light variables
        const cleanVarName = varName.replace('dark-', '');
        darkVars.push(`  --color-${cleanVarName}: ${value};`);
      } else {
        lightVars.push(`  --color-${varName}: ${value};`);
      }
    }

    // Rebuild CSS with proper structure
    let newCssContent = `/**
 * Do not edit directly
 * Generated on ${new Date().toUTCString()}
 */

:root {
${lightVars.join('\n')}
}
`;

    if (darkVars.length > 0) {
      newCssContent += `
[data-theme="dark"] {
${darkVars.join('\n')}
}
`;
    }

    fs.writeFileSync(cssFilePath, newCssContent);

    console.log('✅ Tokens built successfully!');
    console.log('📁 Generated files:');
    console.log('   - build/css/variables.css');
    console.log('   - src/styles/colors.ts');

  } catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
  }
}

buildTokens();
