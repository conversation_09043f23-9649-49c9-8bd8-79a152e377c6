import React from 'react';
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';

// Root component
export interface DropdownProps {
  children: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  modal?: boolean;
}

export const Dropdown: React.FC<DropdownProps> = ({
  children,
  open,
  onOpenChange,
  modal = true,
}) => {
  return (
    <DropdownMenu.Root open={open} onOpenChange={onOpenChange} modal={modal}>
      {children}
    </DropdownMenu.Root>
  );
};

// Trigger component
export interface DropdownTriggerProps {
  children: React.ReactNode;
  asChild?: boolean;
  className?: string;
}

export const DropdownTrigger: React.FC<DropdownTriggerProps> = ({
  children,
  asChild = false,
  className,
}) => {
  return (
    <DropdownMenu.Trigger asChild={asChild} className={className}>
      {children}
    </DropdownMenu.Trigger>
  );
};

// Content component
export interface DropdownContentProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  side?: 'top' | 'right' | 'bottom' | 'left';
  align?: 'start' | 'center' | 'end';
  sideOffset?: number;
  alignOffset?: number;
  avoidCollisions?: boolean;
}

export const DropdownContent: React.FC<DropdownContentProps> = ({
  children,
  className,
  style,
  side = 'bottom',
  align = 'start',
  sideOffset = 4,
  alignOffset = 0,
  avoidCollisions = true,
}) => {
  return (
    <DropdownMenu.Portal>
      <DropdownMenu.Content
        className={className}
        style={style}
        side={side}
        align={align}
        sideOffset={sideOffset}
        alignOffset={alignOffset}
        avoidCollisions={avoidCollisions}
      >
        {children}
      </DropdownMenu.Content>
    </DropdownMenu.Portal>
  );
};

// Item component
export interface DropdownItemProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onSelect?: (event: Event) => void;
  disabled?: boolean;
  textValue?: string;
}

export const DropdownItem: React.FC<DropdownItemProps> = ({
  children,
  className,
  style,
  onSelect,
  disabled = false,
  textValue,
}) => {
  return (
    <DropdownMenu.Item
      className={className}
      style={style}
      onSelect={onSelect}
      disabled={disabled}
      textValue={textValue}
    >
      {children}
    </DropdownMenu.Item>
  );
};

// Separator component
export interface DropdownSeparatorProps {
  className?: string;
  style?: React.CSSProperties;
}

export const DropdownSeparator: React.FC<DropdownSeparatorProps> = ({
  className,
  style,
}) => {
  return <DropdownMenu.Separator className={className} style={style} />;
};

// Label component
export interface DropdownLabelProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

export const DropdownLabel: React.FC<DropdownLabelProps> = ({
  children,
  className,
  style,
}) => {
  return (
    <DropdownMenu.Label className={className} style={style}>
      {children}
    </DropdownMenu.Label>
  );
};

// CheckboxItem component
export interface DropdownCheckboxItemProps {
  children: React.ReactNode;
  className?: string;
  checked?: boolean | 'indeterminate';
  onCheckedChange?: (checked: boolean) => void;
  disabled?: boolean;
  textValue?: string;
}

export const DropdownCheckboxItem: React.FC<DropdownCheckboxItemProps> = ({
  children,
  className,
  checked,
  onCheckedChange,
  disabled = false,
  textValue,
}) => {
  return (
    <DropdownMenu.CheckboxItem
      className={className}
      checked={checked}
      onCheckedChange={onCheckedChange}
      disabled={disabled}
      textValue={textValue}
    >
      {children}
    </DropdownMenu.CheckboxItem>
  );
};

// RadioGroup component
export interface DropdownRadioGroupProps {
  children: React.ReactNode;
  value?: string;
  onValueChange?: (value: string) => void;
}

export const DropdownRadioGroup: React.FC<DropdownRadioGroupProps> = ({
  children,
  value,
  onValueChange,
}) => {
  return (
    <DropdownMenu.RadioGroup value={value} onValueChange={onValueChange}>
      {children}
    </DropdownMenu.RadioGroup>
  );
};

// RadioItem component
export interface DropdownRadioItemProps {
  children: React.ReactNode;
  className?: string;
  value: string;
  disabled?: boolean;
  textValue?: string;
}

export const DropdownRadioItem: React.FC<DropdownRadioItemProps> = ({
  children,
  className,
  value,
  disabled = false,
  textValue,
}) => {
  return (
    <DropdownMenu.RadioItem
      className={className}
      value={value}
      disabled={disabled}
      textValue={textValue}
    >
      {children}
    </DropdownMenu.RadioItem>
  );
};

// ItemIndicator component
export interface DropdownItemIndicatorProps {
  children: React.ReactNode;
  className?: string;
}

export const DropdownItemIndicator: React.FC<DropdownItemIndicatorProps> = ({
  children,
  className,
}) => {
  return (
    <DropdownMenu.ItemIndicator className={className}>
      {children}
    </DropdownMenu.ItemIndicator>
  );
};

// Sub components for nested dropdowns
export interface DropdownSubProps {
  children: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export const DropdownSub: React.FC<DropdownSubProps> = ({
  children,
  open,
  onOpenChange,
}) => {
  return (
    <DropdownMenu.Sub open={open} onOpenChange={onOpenChange}>
      {children}
    </DropdownMenu.Sub>
  );
};

export interface DropdownSubTriggerProps {
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  textValue?: string;
}

export const DropdownSubTrigger: React.FC<DropdownSubTriggerProps> = ({
  children,
  className,
  disabled = false,
  textValue,
}) => {
  return (
    <DropdownMenu.SubTrigger
      className={className}
      disabled={disabled}
      textValue={textValue}
    >
      {children}
    </DropdownMenu.SubTrigger>
  );
};

export interface DropdownSubContentProps {
  children: React.ReactNode;
  className?: string;
  side?: 'top' | 'right' | 'bottom' | 'left';
  align?: 'start' | 'center' | 'end';
  sideOffset?: number;
  alignOffset?: number;
  avoidCollisions?: boolean;
}

export const DropdownSubContent: React.FC<DropdownSubContentProps> = ({
  children,
  className,
  side = 'right',
  align = 'start',
  sideOffset = 0,
  alignOffset = 0,
  avoidCollisions = true,
}) => {
  return (
    <DropdownMenu.Portal>
      <DropdownMenu.SubContent
        className={className}
        side={side}
        align={align}
        sideOffset={sideOffset}
        alignOffset={alignOffset}
        avoidCollisions={avoidCollisions}
      >
        {children}
      </DropdownMenu.SubContent>
    </DropdownMenu.Portal>
  );
};
