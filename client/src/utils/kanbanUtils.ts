import { Task, TaskStatus } from '../types';
import { Column, Task as KanbanTask } from '@/components';

/**
 * Transform a backend Task into a KanbanTask
 */
export const transformTaskToKanbanTask = (task: Task): KanbanTask => {
  return {
    id: task.id.toString(), // Convert number to string for Kanban
    title: task.title,
    description: task.description || '',
  };
};

/**
 * Transform backend tasks into Kanban columns based on task status
 */
export const transformTasksToKanbanColumns = (tasks: Task[]): Column[] => {
  // Separate tasks by their status field
  const todoTasks = tasks.filter(task => task.status === TaskStatus.TODO);
  const inProgressTasks = tasks.filter(task => task.status === TaskStatus.IN_PROGRESS);
  const doneTasks = tasks.filter(task => task.status === TaskStatus.DONE);

  return [
    {
      id: 'todo',
      title: 'To Do',
      tasks: todoTasks.map(transformTaskToKanbanTask),
    },
    {
      id: 'in-progress',
      title: 'In Progress',
      tasks: inProgressTasks.map(transformTaskToKanbanTask),
    },
    {
      id: 'done',
      title: 'Completed',
      tasks: doneTasks.map(transformTaskToKanbanTask),
    },
  ];
};



/**
 * Transform Kanban task back to backend task ID for operations
 */
export const getTaskIdFromKanbanTask = (kanbanTaskId: string): number => {
  return parseInt(kanbanTaskId, 10);
};

/**
 * Get the task status from column ID
 */
export const getTaskStatusFromColumnId = (columnId: string): { status: TaskStatus; completed: boolean } => {
  switch (columnId) {
    case 'todo':
      return { status: TaskStatus.TODO, completed: false };
    case 'in-progress':
      return { status: TaskStatus.IN_PROGRESS, completed: false };
    case 'done':
      return { status: TaskStatus.DONE, completed: true };
    default:
      return { status: TaskStatus.TODO, completed: false };
  }
};
