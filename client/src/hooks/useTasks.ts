import { useEffect } from 'react';
import { useTaskStore } from '../store/taskStore';
import { useCharacterStore } from '../store/characterStore';

export const useTasks = () => {
  const { 
    tasks, 
    isLoading, 
    error, 
    fetchTasks, 
    createTask, 
    updateTask, 
    completeTask, 
    deleteTask, 
    clearError 
  } = useTaskStore();

  const { gainExperience, playAnimation } = useCharacterStore();

  useEffect(() => {
    fetchTasks();
  }, [fetchTasks]);

  const handleCompleteTask = async (taskId: number) => {
    try {
      await completeTask(taskId);
      // Gain experience and play animation when task is completed
      gainExperience(25);
      playAnimation('celebrate', 1500);
    } catch (error) {
      console.error('Failed to complete task:', error);
    }
  };

  return {
    tasks,
    isLoading,
    error,
    createTask,
    updateTask,
    completeTask: handleCompleteTask,
    deleteTask,
    clearError,
    refetch: fetchTasks,
  };
};
