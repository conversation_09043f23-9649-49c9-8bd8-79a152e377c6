import { useState, useEffect, useCallback } from 'react';
import { useTasks } from './useTasks';
import { Column } from '@/components';
import {
  transformTasksToKanbanColumns,
  getTaskIdFromKanbanTask,
  getTaskStatusFromColumnId
} from '../utils/kanbanUtils';

export const useKanbanTasks = () => {
  const {
    tasks,
    isLoading,
    error,
    updateTask,
    completeTask,
    clearError,
    refetch
  } = useTasks();

  const [kanbanColumns, setKanbanColumns] = useState<Column[]>([]);

  // Transform backend tasks to Kanban columns whenever tasks change
  useEffect(() => {
    const columns = transformTasksToKanbanColumns(tasks);
    setKanbanColumns(columns);
  }, [tasks]);

  // Handle moving tasks between columns
  const handleTaskMove = useCallback(async (
    taskId: string,
    fromColumnId: string,
    toColumnId: string
  ) => {
    const backendTaskId = getTaskIdFromKanbanTask(taskId);
    const newStatusData = getTaskStatusFromColumnId(toColumnId);

    try {
      if (toColumnId === 'done') {
        // Complete the task (this will also set status to DONE in the backend)
        await completeTask(backendTaskId);
      } else {
        // Update task status and completion state
        await updateTask(backendTaskId, {
          status: newStatusData.status,
          completed: newStatusData.completed
        });
      }

      // Refetch tasks to get updated data
      await refetch();
    } catch (error) {
      console.error('Failed to move task:', error);
      // Optionally show error to user
    }
  }, [updateTask, completeTask, refetch]);

  // Handle task reordering within the same column
  const handleTaskReorder = useCallback(async (
    taskId: string,
    columnId: string,
    newIndex: number
  ) => {
    // For reordering within the same column, we don't need to change backend status
    // The KanbanContainer will handle the local state update for visual reordering
    // The in-progress state is maintained separately and doesn't affect ordering
    console.log(`Reordering task ${taskId} in column ${columnId} to position ${newIndex}`);
  }, []);

  return {
    kanbanColumns,
    isLoading,
    error,
    clearError,
    handleTaskMove,
    handleTaskReorder,
    refetch
  };
};
