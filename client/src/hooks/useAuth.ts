import { useEffect } from 'react';
import { useAuthStore } from '../store/authStore';

export const useAuth = () => {
  const { 
    user, 
    isAuthenticated, 
    isLoading, 
    error, 
    login, 
    register, 
    logout, 
    getCurrentUser, 
    clearError 
  } = useAuthStore();

  useEffect(() => {
    // Check if user is already logged in on app start
    const token = localStorage.getItem('access_token');
    if (token && !user) {
      getCurrentUser();
    }
  }, [user, getCurrentUser]);

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout,
    clearError,
  };
};
