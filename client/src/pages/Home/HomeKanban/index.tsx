import { Flex } from '@DS/core';
import React from 'react';
import { KanbanContainer, KanbanFilterMenu } from '@/components';
import { useKanbanTasks } from '../../../hooks/useKanbanTasks';
import './styles.css';

const HomeKanban: React.FC = () => {
  const {
    kanbanColumns,
    isLoading,
    error,
    clearError,
    handleTaskMove,
    handleTaskReorder
  } = useKanbanTasks();

  if (isLoading && kanbanColumns.length === 0) {
    return (
      <Flex justify="center" className="kanbanBoardContainer">
        <Flex direction="column" align="center" className="kanbanContainer">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span className="ml-2">Loading tasks...</span>
          </div>
        </Flex>
      </Flex>
    );
  }

  return (
    <Flex justify="center" className="kanbanBoardContainer">
      <Flex direction="column" align="center" className="kanbanContainer">
        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center justify-between">
              <p className="text-sm text-red-700">{error}</p>
              <button
                onClick={clearError}
                className="text-red-400 hover:text-red-600"
              >
                ×
              </button>
            </div>
          </div>
        )}

        <KanbanFilterMenu />
        <KanbanContainer
          initialColumns={kanbanColumns}
          onTaskMove={handleTaskMove}
          onTaskReorder={handleTaskReorder}
        />
      </Flex>
    </Flex>
  );
};

export default HomeKanban;