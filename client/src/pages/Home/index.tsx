import React from 'react';
import { Navbar } from '@/components/Layout/Navbar';
import HomeKanban from './HomeKanban';
import { Flex } from '../../../DS/src/core';
import { AnimatedHumanSprite } from '@/components/Character/AnimatedHumanSprite';

const Home: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <Flex direction="column" style={{ height: 'calc(100vh - 72px)' }}>
          <AnimatedHumanSprite width="100%" height={288} />
          <HomeKanban />
      </Flex>
    </div>
  );
};

export default Home