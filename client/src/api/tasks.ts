import { apiClient } from './client';
import { Task, TaskCreate, TaskUpdate } from '../types';

export const tasksApi = {
  getTasks: async (): Promise<Task[]> => {
    const response = await apiClient.get('/tasks/');
    return response.data;
  },

  createTask: async (taskData: TaskCreate): Promise<Task> => {
    const response = await apiClient.post('/tasks/', taskData);
    return response.data;
  },

  updateTask: async (taskId: number, taskData: TaskUpdate): Promise<Task> => {
    const response = await apiClient.put(`/tasks/${taskId}`, taskData);
    return response.data;
  },

  completeTask: async (taskId: number): Promise<Task> => {
    const response = await apiClient.post(`/tasks/${taskId}/complete`);
    return response.data;
  },

  deleteTask: async (taskId: number): Promise<void> => {
    await apiClient.delete(`/tasks/${taskId}`);
  },

  getTask: async (taskId: number): Promise<Task> => {
    const response = await apiClient.get(`/tasks/${taskId}`);
    return response.data;
  },
};
