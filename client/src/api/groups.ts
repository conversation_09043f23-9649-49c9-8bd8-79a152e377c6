import { apiClient } from './client';
import { Group } from '../types';

export const groupsApi = {
  getGroups: async (): Promise<Group[]> => {
    const response = await apiClient.get('/groups/');
    return response.data;
  },

  createGroup: async (groupData: { name: string; description?: string }): Promise<Group> => {
    const response = await apiClient.post('/groups/', groupData);
    return response.data;
  },

  getGroup: async (groupId: number): Promise<Group> => {
    const response = await apiClient.get(`/groups/${groupId}`);
    return response.data;
  },

  updateGroup: async (groupId: number, groupData: { name?: string; description?: string }): Promise<Group> => {
    const response = await apiClient.put(`/groups/${groupId}`, groupData);
    return response.data;
  },

  addMember: async (groupId: number, userId: number): Promise<Group> => {
    const response = await apiClient.post(`/groups/${groupId}/members/${userId}`);
    return response.data;
  },

  removeMember: async (groupId: number, userId: number): Promise<Group> => {
    const response = await apiClient.delete(`/groups/${groupId}/members/${userId}`);
    return response.data;
  },

  deleteGroup: async (groupId: number): Promise<void> => {
    await apiClient.delete(`/groups/${groupId}`);
  },
};
