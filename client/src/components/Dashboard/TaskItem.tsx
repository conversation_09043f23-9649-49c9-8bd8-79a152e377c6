import React from 'react';
import { CheckCircle, Circle, Calendar, Trash2, Edit } from 'lucide-react';
import { Task } from '../../types';
import { Button } from '../UI/Button';
import { formatDate, isOverdue, getDaysUntilDue } from '../../utils/dateUtils';
import { cn } from '../../utils/cn';

interface TaskItemProps {
  task: Task;
  onComplete: (taskId: number) => void;
  onEdit: (task: Task) => void;
  onDelete: (taskId: number) => void;
}

export const TaskItem: React.FC<TaskItemProps> = ({
  task,
  onComplete,
  onEdit,
  onDelete,
}) => {
  const daysUntilDue = getDaysUntilDue(task.due_date);
  const overdue = isOverdue(task.due_date);

  const handleCompleteClick = () => {
    if (!task.completed) {
      onComplete(task.id);
    }
  };

  return (
    <div
      className={cn(
        'flex items-center space-x-4 p-4 border rounded-lg transition-all duration-200',
        task.completed
          ? 'bg-success-50 border-success-200'
          : overdue
          ? 'bg-red-50 border-red-200'
          : 'bg-white border-gray-200 hover:border-gray-300'
      )}
    >
      {/* Completion Checkbox */}
      <button
        onClick={handleCompleteClick}
        disabled={task.completed}
        className={cn(
          'flex-shrink-0 transition-colors',
          task.completed
            ? 'text-success-600 cursor-default'
            : 'text-gray-400 hover:text-primary-600 cursor-pointer'
        )}
      >
        {task.completed ? (
          <CheckCircle className="h-6 w-6" />
        ) : (
          <Circle className="h-6 w-6" />
        )}
      </button>

      {/* Task Content */}
      <div className="flex-1 min-w-0">
        <h3
          className={cn(
            'text-sm font-medium',
            task.completed
              ? 'text-gray-500 line-through'
              : 'text-gray-900'
          )}
        >
          {task.title}
        </h3>
        {task.description && (
          <p
            className={cn(
              'text-sm mt-1',
              task.completed
                ? 'text-gray-400'
                : 'text-gray-600'
            )}
          >
            {task.description}
          </p>
        )}
        
        {/* Due Date */}
        {task.due_date && (
          <div className="flex items-center mt-2 text-xs">
            <Calendar className="h-3 w-3 mr-1" />
            <span
              className={cn(
                overdue && !task.completed
                  ? 'text-red-600 font-medium'
                  : task.completed
                  ? 'text-gray-400'
                  : 'text-gray-500'
              )}
            >
              {formatDate(task.due_date)}
              {daysUntilDue !== null && !task.completed && (
                <span className="ml-1">
                  {daysUntilDue === 0
                    ? '(Due today)'
                    : daysUntilDue > 0
                    ? `(${daysUntilDue} days left)`
                    : `(${Math.abs(daysUntilDue)} days overdue)`}
                </span>
              )}
            </span>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onEdit(task)}
          disabled={task.completed}
        >
          <Edit className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onDelete(task.id)}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};
