import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Flex, Button, Text } from '../../../../DS/src/core';
import colors from '@/utils/colors';

interface NavbarProps {
  className?: string;
}

export const Navbar: React.FC<NavbarProps> = ({ className }) => {
  const navigate = useNavigate();

  const handleLogoClick = () => {
    navigate('/home');
  };

  const handleHomeClick = () => {
    navigate('/home');
  };

  const handleGroupClick = () => {
    navigate('#');
  };

  return (
    <Flex
      justify="between"
      align="center"
      p="4"
      style={{
        backgroundColor: colors.core.blue, // Correct blue color from the Figma design
        height: '72px',
      }}
      className={className}
    >
      {/* Logo on the left */}
      <Text
        weight="bold"
        style={{
          cursor: 'pointer',
          color: '#fff',
          fontSize: '24px'
        }}
        onClick={handleLogoClick}
      >
        Kanban RPG
      </Text>

      {/* Navigation buttons in the center */}
      <Flex gap="4" align="center">
        <Button
          variant="ghost"
          onClick={handleHomeClick}
          style={{
            color: '#fff',
            backgroundColor: 'transparent',
            border: 'none',
            cursor: 'pointer',
            fontSize: '18px',
            fontWeight: 'bold',
          }}
        >
          Home
        </Button>
        <Button
          variant="ghost"
          onClick={handleGroupClick}
          style={{
            color: '#fff',
            backgroundColor: 'transparent',
            border: 'none',
            cursor: 'pointer',
            fontSize: '18px',
            fontWeight: 'bold',
          }}
        >
          Group
        </Button>
      </Flex>

      {/* Empty space on the right to maintain balance */}
      <div style={{ width: '100px' }} />
    </Flex>
  );
};