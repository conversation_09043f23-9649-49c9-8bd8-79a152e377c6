import React, { useState, useRef, useEffect } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { KanbanColumn, KanbanCard, Task, Column } from './index';
import {
  findTaskById,
  findColumnByTaskId,
  findColumnById,
  playSound,
  handleCrossColumnMove,
  handleSameColumnReorder
} from './utils';

interface KanbanContainerProps {
  initialColumns: Column[];
  onTaskMove?: (taskId: string, fromColumnId: string, toColumnId: string) => Promise<void>;
  onTaskReorder?: (taskId: string, columnId: string, newIndex: number) => Promise<void>;
}

export const KanbanContainer: React.FC<KanbanContainerProps> = ({
  initialColumns,
  onTaskMove,
  onTaskReorder
}) => {
  const [columns, setColumns] = useState<Column[]>(initialColumns);
  const [activeTask, setActiveTask] = useState<Task | null>(null);

  // Audio refs for sound effects
  const pressAudioRef = useRef<HTMLAudioElement | null>(null);
  const successAudioRef = useRef<HTMLAudioElement | null>(null);

  // Initialize audio elements
  useEffect(() => {
    pressAudioRef.current = new Audio('/static/sounds/ui-press.wav');
    pressAudioRef.current.preload = 'auto';

    successAudioRef.current = new Audio('/static/sounds/ui-success.wav');
    successAudioRef.current.preload = 'auto';
  }, []);

  // Update columns when initialColumns change (from backend)
  React.useEffect(() => {
    setColumns(initialColumns);
  }, [initialColumns]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const task = findTaskById(columns, active.id as string);
    setActiveTask(task);
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    // Find the columns
    const activeColumn = findColumnByTaskId(columns, activeId);
    const overColumn = findColumnByTaskId(columns, overId) || findColumnById(columns, overId);

    if (!activeColumn || !overColumn) return;

    // Handle cross-column moves only
    if (activeColumn.id !== overColumn.id) {
      // Call backend callback if provided
      if (onTaskMove) {
        onTaskMove(activeId, activeColumn.id, overColumn.id);
      } else {
        // Fallback to local state update if no callback
        setColumns((currentColumns) =>
          handleCrossColumnMove(currentColumns, activeId, activeColumn, overColumn, overId)
        );
      }
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveTask(null);

    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    // Find the columns
    const activeColumn = findColumnByTaskId(columns, activeId);
    const overColumn = findColumnByTaskId(columns, overId) || findColumnById(columns, overId);

    if (!activeColumn || !overColumn) return;

    // Play sound immediately when drag ends
    playSound(overColumn.id === 'done', pressAudioRef, successAudioRef);

    // Handle same-column reordering
    if (activeColumn.id === overColumn.id) {
      const { newColumns, activeIndex, overIndex } = handleSameColumnReorder(
        columns,
        activeColumn,
        activeId,
        overId
      );

      if (activeIndex !== overIndex) {
        // Always update local state for immediate visual feedback
        setColumns(newColumns);

        // Also call backend callback if provided (for any backend logic)
        if (onTaskReorder) {
          onTaskReorder(activeId, activeColumn.id, overIndex);
        }
      }
    }
  };

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <div className="kanban-board">
        {columns.map((column) => (
          <KanbanColumn key={column.id} column={column} />
        ))}
      </div>

      <DragOverlay>
        {activeTask ? (
          <div className="task-card dragging">
            <KanbanCard task={activeTask} />
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
};
