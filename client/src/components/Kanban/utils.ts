import React from 'react';
import { CSS } from '@dnd-kit/utilities';
import { Task, Column } from './index';

/**
 * Find a task by its ID across all columns
 */
export const findTaskById = (columns: Column[], id: string): Task | null => {
  for (const column of columns) {
    const task = column.tasks.find((task) => task.id === id);
    if (task) return task;
  }
  return null;
};

/**
 * Find the column that contains a specific task
 */
export const findColumnByTaskId = (columns: Column[], taskId: string): Column | null => {
  return columns.find((column) =>
    column.tasks.some((task) => task.id === taskId)
  ) || null;
};

/**
 * Find a column by its ID
 */
export const findColumnById = (columns: Column[], columnId: string): Column | null => {
  return columns.find((column) => column.id === columnId) || null;
};

/**
 * Play appropriate sound based on drop target
 */
export const playSound = (
  isDroppedOnDone: boolean,
  pressAudioRef: React.RefObject<HTMLAudioElement>,
  successAudioRef: React.RefObject<HTMLAudioElement>
) => {
  const audioToPlay = isDroppedOnDone ? successAudioRef.current : pressAudioRef.current;
  if (audioToPlay) {
    audioToPlay.currentTime = 0; // Reset to start
    audioToPlay.play().catch(console.error);
  }
};

/**
 * Handle moving a task from one column to another
 */
export const handleCrossColumnMove = (
  columns: Column[],
  activeId: string,
  activeColumn: Column,
  overColumn: Column,
  overId: string
): Column[] => {
  const activeItems = activeColumn.tasks;
  const overItems = overColumn.tasks;

  const activeIndex = activeItems.findIndex((item) => item.id === activeId);
  const overIndex = overItems.findIndex((item) => item.id === overId);

  const activeTask = activeItems[activeIndex];

  // Remove from active column
  const newActiveItems = activeItems.filter((item) => item.id !== activeId);

  // Add to over column
  const newOverItems = [...overItems];
  if (overIndex >= 0) {
    // Dropping on a specific task
    newOverItems.splice(overIndex, 0, activeTask);
  } else {
    // Dropping on empty column or at the end
    newOverItems.push(activeTask);
  }

  return columns.map((column) => {
    if (column.id === activeColumn.id) {
      return { ...column, tasks: newActiveItems };
    } else if (column.id === overColumn.id) {
      return { ...column, tasks: newOverItems };
    }
    return column;
  });
};

/**
 * Handle reordering tasks within the same column
 */
export const handleSameColumnReorder = (
  columns: Column[],
  activeColumn: Column,
  activeId: string,
  overId: string
): { newColumns: Column[]; activeIndex: number; overIndex: number } => {
  const activeIndex = activeColumn.tasks.findIndex((item) => item.id === activeId);
  const overIndex = activeColumn.tasks.findIndex((item) => item.id === overId);

  const newColumns = columns.map((column) => {
    if (column.id === activeColumn.id) {
      const newTasks = [...column.tasks];
      const [movedTask] = newTasks.splice(activeIndex, 1);
      newTasks.splice(overIndex, 0, movedTask);
      return { ...column, tasks: newTasks };
    }
    return column;
  });

  return { newColumns, activeIndex, overIndex };
};

// KanbanCard utility functions

/**
 * Calculate the style for a draggable card
 */
export const calculateCardStyle = (
  transform: any,
  transition: string | undefined,
  isDragging: boolean
) => {
  return {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };
};

// KanbanColumn utility functions

/**
 * Get the sortable items for a column (task IDs)
 */
export const getColumnSortableItems = (tasks: Task[]): string[] => {
  return tasks.map(task => task.id);
};

// KanbanFilterMenu utility functions

/**
 * Handle filter selection
 */
export const handleFilterSelect = (filterType: string, onFilterChange?: (filter: string) => void) => {
  console.log(`Filter selected: ${filterType}`);
  if (onFilterChange) {
    onFilterChange(filterType);
  }
};

/**
 * Handle task creation workflow
 */
export const handleTaskCreation = async (
  createTaskFn: (data: any) => Promise<void>,
  taskData: any,
  setShowForm: (show: boolean) => void
) => {
  try {
    await createTaskFn(taskData);
    setShowForm(false);
  } catch (error) {
    console.error('Failed to create task:', error);
    throw error;
  }
};
