import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { calculateCardStyle } from './utils';

export interface Task {
  id: string;
  title: string;
  description: string;
}

interface KanbanCardProps {
  task: Task;
}

export const KanbanCard: React.FC<KanbanCardProps> = ({ task }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: task.id });

  // Calculate card style based on drag state
  const style = calculateCardStyle(transform, transition, isDragging);

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="task-card"
    >
      <h4>{task.title}</h4>
      <p>{task.description}</p>
    </div>
  );
};
