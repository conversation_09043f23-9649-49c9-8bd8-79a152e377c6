import { create } from 'zustand';
import { Task, TaskCreate, TaskUpdate } from '../types';
import { tasksApi } from '../api/tasks';

interface TaskState {
  tasks: Task[];
  isLoading: boolean;
  error: string | null;
  fetchTasks: () => Promise<void>;
  createTask: (taskData: TaskCreate) => Promise<void>;
  updateTask: (taskId: number, taskData: TaskUpdate) => Promise<void>;
  completeTask: (taskId: number) => Promise<void>;
  deleteTask: (taskId: number) => Promise<void>;
  clearError: () => void;
}

export const useTaskStore = create<TaskState>((set, get) => ({
  tasks: [],
  isLoading: false,
  error: null,

  fetchTasks: async () => {
    set({ isLoading: true, error: null });
    try {
      const tasks = await tasksApi.getTasks();
      set({ tasks, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.response?.data?.detail || 'Failed to fetch tasks', 
        isLoading: false 
      });
    }
  },

  createTask: async (taskData: TaskCreate) => {
    set({ isLoading: true, error: null });
    try {
      const newTask = await tasksApi.createTask(taskData);
      set(state => ({ 
        tasks: [...state.tasks, newTask], 
        isLoading: false 
      }));
    } catch (error: any) {
      set({ 
        error: error.response?.data?.detail || 'Failed to create task', 
        isLoading: false 
      });
      throw error;
    }
  },

  updateTask: async (taskId: number, taskData: TaskUpdate) => {
    set({ isLoading: true, error: null });
    try {
      const updatedTask = await tasksApi.updateTask(taskId, taskData);
      set(state => ({
        tasks: state.tasks.map(task => 
          task.id === taskId ? updatedTask : task
        ),
        isLoading: false
      }));
    } catch (error: any) {
      set({ 
        error: error.response?.data?.detail || 'Failed to update task', 
        isLoading: false 
      });
      throw error;
    }
  },

  completeTask: async (taskId: number) => {
    try {
      const completedTask = await tasksApi.completeTask(taskId);
      set(state => ({
        tasks: state.tasks.map(task => 
          task.id === taskId ? completedTask : task
        )
      }));
    } catch (error: any) {
      set({ 
        error: error.response?.data?.detail || 'Failed to complete task'
      });
      throw error;
    }
  },

  deleteTask: async (taskId: number) => {
    set({ isLoading: true, error: null });
    try {
      await tasksApi.deleteTask(taskId);
      set(state => ({
        tasks: state.tasks.filter(task => task.id !== taskId),
        isLoading: false
      }));
    } catch (error: any) {
      set({ 
        error: error.response?.data?.detail || 'Failed to delete task', 
        isLoading: false 
      });
      throw error;
    }
  },

  clearError: () => set({ error: null }),
}));
