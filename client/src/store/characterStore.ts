import { create } from 'zustand';
import { Character, Animation } from '../types';

interface CharacterState {
  character: Character;
  currentAnimation: Animation;
  updateCharacter: (updates: Partial<Character>) => void;
  gainExperience: (amount: number) => void;
  playAnimation: (type: Animation['type'], duration?: number) => void;
  stopAnimation: () => void;
}

const initialCharacter: Character = {
  level: 1,
  experience: 0,
  equipment: {
    weapon: 'sword',
    armor: 'basic',
    accessory: undefined,
  },
  stats: {
    strength: 10,
    defense: 8,
    speed: 12,
  },
};

const initialAnimation: Animation = {
  type: 'idle',
  duration: 0,
  isPlaying: false,
};

export const useCharacterStore = create<CharacterState>((set, get) => ({
  character: initialCharacter,
  currentAnimation: initialAnimation,

  updateCharacter: (updates: Partial<Character>) => {
    set(state => ({
      character: { ...state.character, ...updates }
    }));
  },

  gainExperience: (amount: number) => {
    set(state => {
      const newExperience = state.character.experience + amount;
      const experienceForNextLevel = state.character.level * 100;
      
      if (newExperience >= experienceForNextLevel) {
        // Level up!
        const newLevel = state.character.level + 1;
        const remainingExperience = newExperience - experienceForNextLevel;
        
        // Play level up animation
        get().playAnimation('levelUp', 2000);
        
        return {
          character: {
            ...state.character,
            level: newLevel,
            experience: remainingExperience,
            stats: {
              strength: state.character.stats.strength + 2,
              defense: state.character.stats.defense + 2,
              speed: state.character.stats.speed + 1,
            }
          }
        };
      }
      
      return {
        character: {
          ...state.character,
          experience: newExperience,
        }
      };
    });
  },

  playAnimation: (type: Animation['type'], duration = 1000) => {
    set({
      currentAnimation: {
        type,
        duration,
        isPlaying: true,
      }
    });

    // Auto-stop animation after duration
    setTimeout(() => {
      set({
        currentAnimation: {
          type: 'idle',
          duration: 0,
          isPlaying: false,
        }
      });
    }, duration);
  },

  stopAnimation: () => {
    set({
      currentAnimation: {
        type: 'idle',
        duration: 0,
        isPlaying: false,
      }
    });
  },
}));
