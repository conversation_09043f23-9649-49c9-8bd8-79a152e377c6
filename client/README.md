# Habit Tracker Game - Frontend

A React + PixiJS frontend for the habit tracking game with character progression and task management.

## Features

- **Task Dashboard**: Interactive task list with completion checkboxes
- **PixiJS Character**: Layered sprite system with equipment and animations
- **Character Progression**: Level up system with experience points and stat increases
- **Animations**: Character animations triggered on task completion (celebrate, level-up, attack)
- **Authentication**: Login/register forms with JWT token management
- **State Management**: Zustand for global state management
- **Responsive Design**: Modern UI built with Tailwind CSS
- **Real-time Updates**: Automatic character progression when completing tasks

## Tech Stack

- **React 18** with TypeScript
- **PixiJS 7** for 2D graphics and animations
- **Zustand** for state management
- **Axios** for API communication
- **React Router** for navigation
- **React Hook Form** for form handling
- **Tailwind CSS** for styling
- **Vite** for development and building
- **Lucide React** for icons

## Project Structure

```
client/
├── public/                 # Static assets
├── src/
│   ├── api/               # API service layer
│   │   ├── client.ts      # Axios configuration
│   │   ├── auth.ts        # Authentication API
│   │   ├── tasks.ts       # Tasks API
│   │   └── groups.ts      # Groups API
│   ├── components/        # React components
│   │   ├── Auth/          # Authentication components
│   │   ├── Character/     # PixiJS character components
│   │   ├── Dashboard/     # Task management components
│   │   ├── Layout/        # Layout components
│   │   └── UI/            # Reusable UI components
│   ├── hooks/             # Custom React hooks
│   ├── pages/             # Page components
│   ├── store/             # Zustand stores
│   ├── types/             # TypeScript type definitions
│   ├── utils/             # Utility functions
│   ├── App.tsx            # Main app component
│   ├── main.tsx           # App entry point
│   └── index.css          # Global styles
├── package.json
├── vite.config.ts
├── tailwind.config.js
└── .env                   # Environment variables
```

## Quick Start

### Prerequisites

- Node.js 18+ and Yarn
- Backend API running on `http://localhost:8000`

### Installation

1. **Navigate to the client directory**:
   ```bash
   cd client
   ```

2. **Install dependencies**:
   ```bash
   yarn install
   ```

3. **Start the development server**:
   ```bash
   yarn dev
   ```

4. **Open your browser**:
   Navigate to `http://localhost:3000`

### Environment Variables

Create a `.env` file in the client directory:

```env
VITE_API_BASE_URL=http://localhost:8000
```

## Usage

### Authentication

1. **Register**: Create a new account with username, email, and password
2. **Login**: Sign in with your credentials
3. **Auto-login**: The app remembers your session using JWT tokens

### Task Management

1. **Create Tasks**: Click "Add Task" to create new tasks with optional due dates
2. **Complete Tasks**: Click the circle icon to mark tasks as complete
3. **Edit Tasks**: Use the edit button to modify task details
4. **Delete Tasks**: Remove tasks you no longer need
5. **Filter Tasks**: View all, pending, completed, or overdue tasks

### Character Progression

1. **Gain Experience**: Complete tasks to earn 25 XP each
2. **Level Up**: Reach 100 XP per level to advance
3. **Stat Growth**: Each level increases Strength (+2), Defense (+2), and Speed (+1)
4. **Animations**: Watch your character celebrate when completing tasks and level up

### Character Features

- **Layered Sprites**: Base character with equipment overlays
- **Equipment System**: Weapons (sword, staff) and armor (basic, leather, chainmail)
- **Animations**: 
  - Idle (default state)
  - Celebrate (task completion)
  - Level Up (when gaining a level)
  - Attack (combat animation)
- **Stats Display**: Real-time character stats and experience bar

## API Integration

The frontend communicates with the FastAPI backend using Axios:

- **Base URL**: Configured via environment variable
- **Authentication**: JWT tokens stored in localStorage
- **Auto-retry**: Automatic token refresh on 401 errors
- **Error Handling**: User-friendly error messages

### API Endpoints Used

- `POST /auth/register` - User registration
- `POST /auth/login-json` - User login
- `GET /users/me` - Get current user
- `GET /tasks/` - Get user tasks
- `POST /tasks/` - Create new task
- `PUT /tasks/{id}` - Update task
- `POST /tasks/{id}/complete` - Complete task
- `DELETE /tasks/{id}` - Delete task

## Development

### Available Scripts

```bash
# Start development server
yarn dev

# Build for production
yarn build

# Preview production build
yarn preview

# Run linting
yarn lint
```

### Code Organization

- **Components**: Organized by feature (Auth, Dashboard, Character, etc.)
- **Stores**: Separate Zustand stores for different domains (auth, tasks, character)
- **API Layer**: Centralized API calls with error handling
- **Types**: Comprehensive TypeScript interfaces
- **Hooks**: Custom hooks for common functionality

### Character Animation System

The PixiJS character system supports:

1. **Layered Rendering**: Base character + equipment overlays
2. **Animation States**: Different animations for various actions
3. **Equipment Visualization**: Visual representation of character gear
4. **Stat Integration**: Character appearance reflects progression

### State Management

Using Zustand for:

- **Auth State**: User authentication and session management
- **Task State**: Task CRUD operations and filtering
- **Character State**: Character progression and animation control

## Customization

### Adding New Animations

1. Define animation type in `types/index.ts`
2. Implement animation logic in `CharacterSprite.tsx`
3. Trigger animation from appropriate component

### Adding New Equipment

1. Update character equipment types
2. Add sprite rendering logic in `createWeaponSprite` or `createArmorSprite`
3. Update character store with new equipment options

### Styling

- **Tailwind CSS**: Utility-first CSS framework
- **Custom Components**: Reusable UI components in `components/UI/`
- **Responsive Design**: Mobile-first approach
- **Dark Mode**: Can be easily added with Tailwind's dark mode utilities

## Production Build

```bash
# Build for production
yarn build

# The build artifacts will be stored in the `dist/` directory
```

### Deployment

The built application can be deployed to any static hosting service:

- **Vercel**: `yarn build && vercel --prod`
- **Netlify**: `yarn build` then drag and drop `dist/` folder
- **GitHub Pages**: Use GitHub Actions with `yarn build`
- **AWS S3**: `yarn build` then upload `dist/` contents

## Troubleshooting

### Common Issues

1. **API Connection**: Ensure backend is running on `http://localhost:8000`
2. **CORS Errors**: Backend should allow frontend origin
3. **Token Expiration**: Tokens expire after 30 minutes by default
4. **PixiJS Performance**: Reduce animation complexity on slower devices
5. **yarn install fails:**
   ```bash
   # Clear yarn cache
   yarn cache clean
   # Delete node_modules and reinstall
   rm -rf node_modules yarn.lock
   yarn install
   ```

### Development Tips

1. **Hot Reload**: Vite provides fast hot module replacement
2. **TypeScript**: Use strict typing for better development experience
3. **Component Testing**: Test components in isolation
4. **State Debugging**: Use browser dev tools with Zustand devtools

## Contributing

1. Follow the existing code structure and naming conventions
2. Add TypeScript types for new features
3. Test components thoroughly
4. Update documentation for new features

## License

This project is licensed under the MIT License.
